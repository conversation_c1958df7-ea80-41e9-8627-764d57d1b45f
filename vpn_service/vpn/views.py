"""
Views for VPN app - Stage 1 Focus: SingBox Only
"""
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
import logging
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from drf_spectacular.types import OpenApiTypes

from accounts.models import HiddifyLink, UserDevice
from subscriptions.models import ActiveSubscription
from .services import HiddifyApiService, SingBoxConfigService
from .serializers import VPNConfigExampleSerializer, TrafficStatsExampleSerializer, VpnErrorResponseSerializer, LocationListResponseSerializer
from .models import Location, SubscriptionPlanLocation

logger = logging.getLogger(__name__)


@extend_schema(
    tags=['VPN'],
    summary='Get VPN configuration',
    description='Get SingBox VPN configuration for authenticated user with optional location selection',
    parameters=[
        OpenApiParameter(
            name='location_id',
            type=OpenApiTypes.UUID,
            location=OpenApiParameter.QUERY,
            description='Optional UUID of VPN location. If not provided, default location for user\'s plan will be used.',
            required=False
        )
    ],
    responses={
        200: VPNConfigExampleSerializer,
        400: VpnErrorResponseSerializer,
        403: VpnErrorResponseSerializer,
        404: VpnErrorResponseSerializer
    },
    examples=[
        OpenApiExample(
            'SingBox Config Request with Location',
            description='Request SingBox configuration for specific location',
            value={
                "location_id": "550e8400-e29b-41d4-a716-************"
            }
        ),
        OpenApiExample(
            'SingBox Config Request (Default Location)',
            description='Request SingBox configuration using default location for user\'s plan',
            value={}
        )
    ]
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_vpn_config(request):
    """
    Получение SingBox VPN конфигурации для аутентифицированного пользователя (Stage 1 Focus).

    PURPOSE:
      - Предоставляет актуальную SingBox VPN конфигурацию пользователю
      - Проверяет активность подписки и лимиты
      - Синхронизирует статистику трафика с Hiddify
      - STAGE 1: Поддерживает ТОЛЬКО SingBox конфигурации (без параметра type)

    AAG (Actor -> Action -> Goal):
      - Пользователь -> Запрашивает VPN конфигурацию -> Получает SingBox конфигурацию
      - Система -> Проверяет права доступа -> Возвращает фиксированно SingBox формат

    CONTRACT:
      PRECONDITIONS:
        - Пользователь аутентифицирован (JWT токен)
        - Активная подписка пользователя
        - НЕ требуется параметр type (фиксированно SingBox)
      POSTCONDITIONS:
        - Возвращается SingBox VPN конфигурация в JSON формате
        - config_type всегда равен 'singbox'
        - Обновляется статистика последнего запроса
        - Синхронизируется информация о трафике
      INVARIANTS:
        - Доступ только для пользователей с активной подпиской
        - Конфигурация соответствует текущим лимитам
        - STAGE 1: Всегда возвращается SingBox формат

    NOTES:
      - Stage 1: Параметр type НЕ поддерживается
      - TODO (Stage 2): Добавить поддержку Clash конфигураций с параметром type
      - TODO (Stage 3): Добавить поддержку Subscription ссылок с параметром type
    """
    
    # Шаг 1: Аутентификация и извлечение параметров
    user_account = request.user
    device_id = getattr(request.auth, 'get', lambda x: None)('device_id') if hasattr(request.auth, 'get') else None
    location_id = request.GET.get('location_id')

    # STAGE 1: Фиксированный тип конфигурации - только SingBox
    config_type = 'singbox'

    # TODO (Этап 2): Восстановить поддержку параметра type для Clash
    # TODO (Этап 3): Восстановить поддержку параметра type для Subscription
    # Закомментированный код для будущего использования:
    # config_type = request.GET.get('type', 'singbox')  # singbox, clash, subscription
    # if config_type not in ['singbox', 'clash', 'subscription']:
    #     return Response({
    #         'error': 'Invalid config type. Supported: singbox, clash, subscription'
    #     }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        # Шаг 2: Поиск hiddify_user_uuid в нашей БД
        try:
            hiddify_link = HiddifyLink.objects.get(
                user=user_account,
                is_active_in_hiddify=True
            )
        except HiddifyLink.DoesNotExist:
            return Response({
                'error': 'VPN access not configured. Please contact support.'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Шаг 3: Проверка активной подписки пользователя
        active_subscription = ActiveSubscription.objects.select_related('plan').filter(
            user=user_account,
            is_active=True,
            start_date__lte=timezone.now(),
            end_date__gte=timezone.now()
        ).first()

        if not active_subscription:
            return Response({
                'error': 'No active subscription. Please renew your subscription.'
            }, status=status.HTTP_403_FORBIDDEN)

        # Шаг 4: Определение целевой локации
        target_location = None

        if location_id:
            # Пользователь указал конкретную локацию
            try:
                target_location = Location.objects.get(
                    id=location_id,
                    is_active=True,
                    location_plans__plan=active_subscription.plan
                )
                logger.info(f"User {user_account.id} requested specific location: {target_location.name}")
            except Location.DoesNotExist:
                return Response({
                    'error': 'Specified location is not available for your subscription plan.'
                }, status=status.HTTP_403_FORBIDDEN)
        else:
            # Пользователь не указал локацию, ищем дефолтную или первую доступную
            target_location = Location.objects.filter(
                location_plans__plan=active_subscription.plan,
                location_plans__is_default=True,
                is_active=True
            ).first()

            if not target_location:
                # Дефолтной нет, берем первую доступную
                target_location = Location.objects.filter(
                    location_plans__plan=active_subscription.plan,
                    is_active=True
                ).order_by('country_code', 'city', 'name').first()

            if target_location:
                logger.info(f"User {user_account.id} using default/first location: {target_location.name}")

        if not target_location:
            return Response({
                'error': 'No VPN locations available for your subscription plan. Please contact support.'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Шаг 5: Генерация SingBox конфигурации с использованием выбранной локации
        try:
            # Генерируем конфигурацию на основе локации
            singbox_config = SingBoxConfigService.generate_singbox_config(
                target_location,
                str(hiddify_link.hiddify_user_uuid)
            )

            # Получаем статистику трафика из Hiddify (опционально)
            hiddify_service = HiddifyApiService()
            success, hiddify_response = hiddify_service.get_singbox_config_for_user(
                str(hiddify_link.hiddify_user_uuid)
            )

            # Извлекаем информацию о трафике если доступна
            traffic_info = {}
            if success and 'traffic_info' in hiddify_response:
                traffic_info = hiddify_response['traffic_info']

            logger.info(f"Generated SingBox config for user {user_account.id} using location {target_location.name}")

        except ValueError as e:
            logger.error(f"Failed to generate config for location {target_location.name}: {str(e)}")
            return Response({
                'error': 'Configuration error for selected location. Please try another location or contact support.'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        except Exception as e:
            logger.error(f"Unexpected error generating config for user {user_account.id}: {str(e)}")
            return Response({
                'error': 'Failed to generate VPN configuration. Please try again.'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # Шаг 6: Обновление информации о трафике и устройстве
        if traffic_info:
            # Обновляем статистику в нашей БД
            hiddify_link.traffic_used_bytes = traffic_info.get('download_bytes', 0) + traffic_info.get('upload_bytes', 0)
            hiddify_link.last_traffic_sync = timezone.now()

        # Обновляем время последнего запроса конфигурации
        hiddify_link.last_config_request = timezone.now()
        hiddify_link.save(update_fields=['traffic_used_bytes', 'last_traffic_sync', 'last_config_request'])

        # Обновляем время последнего обращения устройства
        if device_id:
            UserDevice.objects.filter(
                user=user_account,
                device_id=device_id
            ).update(last_seen=timezone.now())

        # Шаг 7: Формирование ответа с динамически сгенерированной конфигурацией
        response_data = {
            'success': True,
            'config_type': 'singbox',  # STAGE 1: всегда SingBox
            'config': singbox_config,
            'location_info': {
                'id': str(target_location.id),
                'name': target_location.name,
                'country_code': target_location.country_code,
                'city': target_location.city,
                'flag_emoji': target_location.flag_emoji
            },
            'subscription_info': {
                'plan_name': active_subscription.plan.name,
                'end_date': active_subscription.end_date.isoformat(),
                'traffic_limit_gb': active_subscription.plan.traffic_limit_gb,
                'traffic_used_gb': round(hiddify_link.traffic_used_bytes / (1024**3), 2) if hiddify_link.traffic_used_bytes else 0
            }
        }

        # TODO (Этап 3): Восстановить специальную обработку для subscription типа
        # Закомментированный код для будущего использования:
        # if config_type == 'subscription':
        #     response_data['links'] = config_response.get('config')

        return Response(response_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"SingBox config request failed for user {user_account.id}: {str(e)}")
        return Response({
            'error': 'Failed to process SingBox VPN configuration request.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    tags=['VPN'],
    summary='Get traffic statistics',
    description='Get traffic usage statistics',
    responses={
        200: TrafficStatsExampleSerializer,
        404: VpnErrorResponseSerializer
    }
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_traffic_stats(request):
    """
    Получение статистики использования трафика.
    
    PURPOSE:
      - Предоставляет актуальную статистику трафика
      - Синхронизирует данные с Hiddify Manager
      - Показывает лимиты и использование
    """
    user_account = request.user
    
    try:
        hiddify_link = HiddifyLink.objects.get(
            user=user_account,
            is_active_in_hiddify=True
        )
        
        active_subscription = ActiveSubscription.objects.filter(
            user=user_account,
            is_active=True,
            start_date__lte=timezone.now(),
            end_date__gte=timezone.now()
        ).first()
        
        if not active_subscription:
            return Response({
                'error': 'No active subscription found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Получаем актуальную статистику из Hiddify через SingBox endpoint (Stage 1 Focus)
        hiddify_service = HiddifyApiService()
        success, config_response = hiddify_service.get_singbox_config_for_user(
            str(hiddify_link.hiddify_user_uuid)
        )

        # TODO (Этап 3): Восстановить использование get_subscription_link_for_user для получения статистики
        # success, config_response = hiddify_service.get_subscription_link_for_user(
        #     str(hiddify_link.hiddify_user_uuid)
        # )
        
        traffic_info = {}
        if success and 'traffic_info' in config_response:
            traffic_info = config_response['traffic_info']
            
            # Обновляем локальную статистику
            hiddify_link.traffic_used_bytes = traffic_info.get('download_bytes', 0) + traffic_info.get('upload_bytes', 0)
            hiddify_link.last_traffic_sync = timezone.now()
            hiddify_link.save(update_fields=['traffic_used_bytes', 'last_traffic_sync'])
        
        return Response({
            'success': True,
            'traffic_stats': {
                'upload_bytes': traffic_info.get('upload_bytes', 0),
                'download_bytes': traffic_info.get('download_bytes', 0),
                'total_used_bytes': traffic_info.get('upload_bytes', 0) + traffic_info.get('download_bytes', 0),
                'total_used_gb': round((traffic_info.get('upload_bytes', 0) + traffic_info.get('download_bytes', 0)) / (1024**3), 2),
                'limit_gb': active_subscription.plan.traffic_limit_gb,
                'limit_bytes': active_subscription.plan.traffic_limit_gb * 1024 * 1024 * 1024,
                'last_sync': hiddify_link.last_traffic_sync.isoformat() if hiddify_link.last_traffic_sync else None
            },
            'subscription_info': {
                'plan_name': active_subscription.plan.name,
                'end_date': active_subscription.end_date.isoformat(),
                'days_remaining': active_subscription.days_remaining
            }
        })
        
    except HiddifyLink.DoesNotExist:
        return Response({
            'error': 'VPN access not configured'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Traffic stats request failed for user {user_account.id}: {str(e)}")
        return Response({
            'error': 'Failed to retrieve traffic statistics'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    tags=['VPN'],
    summary='Get available VPN locations',
    description='Get list of VPN locations available for user\'s subscription plan',
    responses={
        200: LocationListResponseSerializer,
        403: VpnErrorResponseSerializer,
        404: VpnErrorResponseSerializer
    },
    examples=[
        OpenApiExample(
            'Available Locations',
            description='List of VPN locations available for user\'s subscription plan',
            value={
                "success": True,
                "count": 3,
                "locations": [
                    {
                        "id": "550e8400-e29b-41d4-a716-************",
                        "name": "Netherlands - Amsterdam",
                        "country_code": "NL",
                        "city": "Amsterdam",
                        "flag_emoji": "🇳🇱"
                    },
                    {
                        "id": "550e8400-e29b-41d4-a716-446655440001",
                        "name": "Germany - Frankfurt",
                        "country_code": "DE",
                        "city": "Frankfurt",
                        "flag_emoji": "🇩🇪"
                    },
                    {
                        "id": "550e8400-e29b-41d4-a716-446655440002",
                        "name": "United States - New York",
                        "country_code": "US",
                        "city": "New York",
                        "flag_emoji": "🇺🇸"
                    }
                ]
            }
        )
    ]
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_available_locations(request):
    """
    Получение списка доступных VPN локаций для текущего пользователя.

    PURPOSE:
      - Предоставляет пользователю список VPN серверов, доступных по его тарифному плану
      - Фильтрует локации на основе активной подписки пользователя
      - Обеспечивает дифференциацию сервиса по уровням подписки
      - Оптимизирует запросы к базе данных с помощью select_related/prefetch_related

    AAG (Actor -> Action -> Goal):
      - Пользователь -> Запрашивает доступные локации -> Получает список для выбора сервера
      - Система -> Проверяет тарифный план -> Возвращает соответствующие локации
      - Клиент -> Отображает локации -> Позволяет пользователю выбрать оптимальный сервер

    CONTRACT:
      PRECONDITIONS:
        - Пользователь аутентифицирован (JWT токен)
        - Активная подписка пользователя существует
      POSTCONDITIONS:
        - Возвращается список локаций, доступных для тарифного плана пользователя
        - Локации отсортированы по стране и городу
        - Технические параметры (hiddify_params) скрыты от пользователя
      INVARIANTS:
        - Возвращаются только активные локации
        - Локации соответствуют тарифному плану пользователя
        - Структура ответа консистентна

    NOTES:
      - Если у пользователя нет активной подписки, возвращается пустой список
      - Локации кэшируются на уровне базы данных через select_related
      - Эндпоинт оптимизирован для частых запросов от мобильных клиентов
    """
    user_account = request.user

    try:
        # Шаг 1: Проверка активной подписки пользователя
        active_subscription = ActiveSubscription.objects.select_related('plan').filter(
            user=user_account,
            is_active=True,
            start_date__lte=timezone.now(),
            end_date__gte=timezone.now()
        ).first()

        if not active_subscription:
            logger.info(f"User {user_account.id} has no active subscription for locations")
            return Response({
                'success': True,
                'count': 0,
                'locations': []
            }, status=status.HTTP_200_OK)

        # Шаг 2: Получение доступных локаций для тарифного плана с оптимизацией запросов
        available_locations = Location.objects.filter(
            location_plans__plan=active_subscription.plan,
            is_active=True
        ).select_related().order_by('country_code', 'city', 'name')

        # Шаг 3: Сериализация данных локаций (скрываем технические детали)
        locations_data = []
        for location in available_locations:
            locations_data.append({
                'id': str(location.id),
                'name': location.name,
                'country_code': location.country_code,
                'city': location.city,
                'flag_emoji': location.flag_emoji
            })

        logger.info(f"Returned {len(locations_data)} locations for user {user_account.id} with plan {active_subscription.plan.name}")

        return Response({
            'success': True,
            'count': len(locations_data),
            'locations': locations_data
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Failed to get locations for user {user_account.id}: {str(e)}")
        return Response({
            'error': 'Failed to retrieve available locations'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
